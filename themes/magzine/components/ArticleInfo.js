import LazyImage from '@/components/LazyImage'
import NotionIcon from '@/components/NotionIcon'
import { siteConfig } from '@/lib/config'
import { getPostCover } from '@/lib/utils/getFirstImage'
import CategoryItem from './CategoryItem'
import TagItemMini from './TagItemMini'
import CONFIG from '../config'

/**
 * 文章详情页介绍
 * @param {*} props
 * @returns
 */
export default function ArticleInfo(props) {
  const { post } = props
  const coverSource = siteConfig('MAGZINE_POST_COVER_SOURCE', 'pageCover', CONFIG)
  const cover = getPostCover(post, coverSource)

  return (
    <>
      <div className='flex flex-col gap-y-4 py-4 px-2 lg:px-0'>
        <div className='flex justify-center items-center space-x-1'>
          {siteConfig('MAGZINE_POST_LIST_CATEGORY') && (
            <CategoryItem category={post?.category} />
          )}
          <div
            className={
              'flex items-center justify-start flex-wrap text-gray-400'
            }>
            {siteConfig('MAGZINE_POST_LIST_TAG') &&
              post?.tagItems?.map(tag => (
                <TagItemMini key={tag.name} tag={tag} />
              ))}
          </div>
        </div>

        {/* title */}
        <h2 className='text-4xl text-center dark:text-gray-300'>
          {siteConfig('POST_TITLE_ICON') && (
            <NotionIcon icon={post?.pageIcon} />
          )}
          {post?.title}
        </h2>

        <div className='text-xl text-center'>{post?.summary}</div>
      </div>

      {post?.type && !post?.type !== 'Page' && cover && (
        <div className='w-full relative md:flex-shrink-0 overflow-hidden'>
          <LazyImage
            alt={post?.title}
            src={cover}
            className='object-cover max-h-[60vh] w-full'
          />
        </div>
      )}
    </>
  )
}
