# Magzine 主题封面图来源配置功能

## 功能说明

为 NotionNext 的 Magzine 主题添加了封面图来源配置功能，允许用户选择文章封面图的来源：

1. **使用文章封面图** (`pageCover`) - 默认行为，使用 Notion 页面设置的封面图
2. **使用文章第一张图片** (`firstImage`) - 自动提取文章内容中的第一张图片作为封面
3. **自动选择** (`auto`) - 优先使用封面图，如果没有封面图则使用文章第一张图片

## 配置方法

### 1. 修改主题配置

在 `themes/magzine/config.js` 文件中，找到以下配置项：

```javascript
// 封面图来源配置
MAGZINE_POST_COVER_SOURCE: 'pageCover', // 封面图来源：'pageCover' 使用文章封面图，'firstImage' 使用文章第一张图片，'auto' 自动选择（优先使用封面图，没有则使用第一张图片）
```

### 2. 配置选项说明

- `'pageCover'` (默认): 只使用 Notion 页面设置的封面图
- `'firstImage'`: 只使用文章内容中的第一张图片
- `'auto'`: 智能选择，优先使用封面图，没有则使用第一张图片

### 3. 环境变量配置

也可以通过环境变量进行配置：

```bash
# 在 .env.local 文件中设置
NEXT_PUBLIC_MAGZINE_POST_COVER_SOURCE=auto

# 或者在 Vercel 等部署平台的环境变量中设置
NEXT_PUBLIC_MAGZINE_POST_COVER_SOURCE=firstImage
```

### 4. 快速开始

1. 确保你使用的是 magzine 主题：
   ```javascript
   // 在 blog.config.js 中
   THEME: 'magzine'
   ```

2. 修改封面图配置：
   ```javascript
   // 在 themes/magzine/config.js 中
   MAGZINE_POST_COVER_SOURCE: 'auto'
   ```

3. 重启开发服务器：
   ```bash
   npm run dev
   ```

## 使用场景

### 场景1：统一使用文章第一张图片
如果你希望所有文章都使用内容中的第一张图片作为封面，设置为：
```javascript
MAGZINE_POST_COVER_SOURCE: 'firstImage'
```

### 场景2：智能选择封面图
如果你希望有封面图的文章使用封面图，没有封面图的文章自动使用第一张图片，设置为：
```javascript
MAGZINE_POST_COVER_SOURCE: 'auto'
```

### 场景3：保持原有行为
如果你希望保持原有的行为（只使用封面图），设置为：
```javascript
MAGZINE_POST_COVER_SOURCE: 'pageCover'
```

## 技术实现

### 新增文件
- `lib/utils/getFirstImage.js` - 图片提取工具函数

### 修改的组件
- `themes/magzine/components/PostItemCard.js` - 普通文章卡片
- `themes/magzine/components/PostItemCardTop.js` - 置顶文章卡片  
- `themes/magzine/components/PostItemCardWide.js` - 宽版文章卡片
- `themes/magzine/components/ArticleInfo.js` - 文章详情页封面
- `themes/magzine/components/PostGroupLatest.js` - 最新文章列表

### 核心函数

#### `getFirstImageFromPost(post)`
从文章内容中提取第一张图片的 URL。

#### `getPostCover(post, coverSource, fallbackCover)`
根据配置获取文章封面图，支持三种模式。

## 注意事项

1. 该功能仅适用于 Magzine 主题
2. 图片提取按照文章内容的顺序进行，确保获取到真正的"第一张"图片
3. 支持多种图片类型：image、bookmark、embed 等
4. 所有图片 URL 都会经过 `mapImgUrl` 处理，确保兼容性
5. 如果文章没有图片且没有封面图，会使用 fallback 图片（通常是站点默认封面）

## 测试建议

1. 创建一个有封面图的文章，测试 `pageCover` 模式
2. 创建一个没有封面图但有图片的文章，测试 `firstImage` 模式  
3. 创建混合情况的文章，测试 `auto` 模式
4. 验证在首页、文章列表页、文章详情页等不同页面的显示效果
