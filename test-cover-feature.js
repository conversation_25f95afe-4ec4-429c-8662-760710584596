/**
 * 测试封面图功能的简单脚本
 * 运行方式: node test-cover-feature.js
 */

// 模拟导入我们的函数
const { getFirstImageFromPost, getPostCover } = require('./lib/utils/getFirstImage');

// 模拟文章数据
const mockPost1 = {
  id: 'test-1',
  title: '测试文章1 - 有封面图',
  pageCoverThumbnail: 'https://example.com/cover1.jpg',
  content: ['block1', 'block2', 'block3'],
  blockMap: {
    block: {
      'block1': {
        value: {
          type: 'text',
          properties: {
            title: [['这是一段文字']]
          }
        }
      },
      'block2': {
        value: {
          type: 'image',
          properties: {
            source: [['https://example.com/first-image.jpg']]
          }
        }
      },
      'block3': {
        value: {
          type: 'text',
          properties: {
            title: [['更多文字']]
          }
        }
      }
    }
  }
};

const mockPost2 = {
  id: 'test-2',
  title: '测试文章2 - 无封面图',
  pageCoverThumbnail: null,
  content: ['block1', 'block2'],
  blockMap: {
    block: {
      'block1': {
        value: {
          type: 'text',
          properties: {
            title: [['这是一段文字']]
          }
        }
      },
      'block2': {
        value: {
          type: 'image',
          properties: {
            source: [['https://example.com/content-image.jpg']]
          }
        }
      }
    }
  }
};

const mockPost3 = {
  id: 'test-3',
  title: '测试文章3 - 无封面图无内容图片',
  pageCoverThumbnail: null,
  content: ['block1'],
  blockMap: {
    block: {
      'block1': {
        value: {
          type: 'text',
          properties: {
            title: [['只有文字内容']]
          }
        }
      }
    }
  }
};

// 测试函数
function runTests() {
  console.log('🧪 开始测试封面图功能...\n');

  // 测试 getFirstImageFromPost
  console.log('📸 测试 getFirstImageFromPost 函数:');
  console.log('文章1 第一张图片:', getFirstImageFromPost(mockPost1));
  console.log('文章2 第一张图片:', getFirstImageFromPost(mockPost2));
  console.log('文章3 第一张图片:', getFirstImageFromPost(mockPost3));
  console.log('');

  // 测试 getPostCover 不同模式
  console.log('🖼️  测试 getPostCover 函数:');
  
  const fallback = 'https://example.com/default-cover.jpg';
  
  console.log('--- pageCover 模式 ---');
  console.log('文章1:', getPostCover(mockPost1, 'pageCover', fallback));
  console.log('文章2:', getPostCover(mockPost2, 'pageCover', fallback));
  console.log('文章3:', getPostCover(mockPost3, 'pageCover', fallback));
  
  console.log('\n--- firstImage 模式 ---');
  console.log('文章1:', getPostCover(mockPost1, 'firstImage', fallback));
  console.log('文章2:', getPostCover(mockPost2, 'firstImage', fallback));
  console.log('文章3:', getPostCover(mockPost3, 'firstImage', fallback));
  
  console.log('\n--- auto 模式 ---');
  console.log('文章1:', getPostCover(mockPost1, 'auto', fallback));
  console.log('文章2:', getPostCover(mockPost2, 'auto', fallback));
  console.log('文章3:', getPostCover(mockPost3, 'auto', fallback));
  
  console.log('\n✅ 测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
