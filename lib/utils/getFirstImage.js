import { mapImgUrl } from '@/lib/notion/mapImage'

/**
 * 从文章内容中提取第一张图片
 * @param {Object} post - 文章对象
 * @returns {string|null} - 第一张图片的URL，如果没有找到则返回null
 */
export function getFirstImageFromPost(post) {
  if (!post?.blockMap?.block || !post?.content) {
    return null
  }

  const blocks = post.blockMap.block

  // 按照文章内容的顺序遍历块，寻找第一个图片块
  for (const blockId of post.content) {
    const block = blocks[blockId]
    const blockValue = block?.value

    if (!blockValue) continue

    // 检查是否是图片块
    if (blockValue.type === 'image') {
      const imageUrl = blockValue.properties?.source?.[0]?.[0]
      if (imageUrl) {
        // 使用mapImgUrl处理图片URL
        return mapImgUrl(imageUrl, blockValue, 'block')
      }
    }

    // 检查是否是包含图片的其他块类型（如embed、bookmark等）
    if (blockValue.type === 'bookmark') {
      const imageUrl = blockValue.format?.bookmark_cover
      if (imageUrl) {
        return mapImgUrl(imageUrl, blockValue, 'block')
      }
    }

    // 检查是否是embed块中的图片
    if (blockValue.type === 'embed') {
      const imageUrl = blockValue.format?.display_source
      if (imageUrl && (imageUrl.includes('.jpg') || imageUrl.includes('.png') || imageUrl.includes('.gif') || imageUrl.includes('.webp'))) {
        return mapImgUrl(imageUrl, blockValue, 'block')
      }
    }

    // 递归检查子块（如果有的话）
    if (blockValue.content && blockValue.content.length > 0) {
      for (const childBlockId of blockValue.content) {
        const childBlock = blocks[childBlockId]
        const childBlockValue = childBlock?.value

        if (!childBlockValue) continue

        if (childBlockValue.type === 'image') {
          const imageUrl = childBlockValue.properties?.source?.[0]?.[0]
          if (imageUrl) {
            return mapImgUrl(imageUrl, childBlockValue, 'block')
          }
        }
      }
    }
  }

  return null
}

/**
 * 根据配置获取文章封面图
 * @param {Object} post - 文章对象
 * @param {string} coverSource - 封面图来源配置
 * @param {string} fallbackCover - 备用封面图
 * @returns {string|null} - 封面图URL
 */
export function getPostCover(post, coverSource = 'pageCover', fallbackCover = null) {
  if (!post) return fallbackCover
  
  switch (coverSource) {
    case 'firstImage':
      // 只使用文章第一张图片
      return getFirstImageFromPost(post) || fallbackCover
      
    case 'auto':
      // 优先使用封面图，没有则使用第一张图片
      return post.pageCoverThumbnail || getFirstImageFromPost(post) || fallbackCover
      
    case 'pageCover':
    default:
      // 只使用文章封面图
      return post.pageCoverThumbnail || fallbackCover
  }
}
